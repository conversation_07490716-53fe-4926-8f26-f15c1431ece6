# Product Offer System

## Overview

The product offer system has been redesigned to be more ecommerce-friendly. Instead of a separate `OfferProducts` model, offers are now integrated directly into the `Product` model with automatic expiration handling.

## Key Features

### 1. Integrated Offer Fields
- `is_on_offer`: Boolean to enable/disable offers
- `offer_price`: Discounted price when on offer
- `offer_start_date`: When the offer starts (auto-set if not provided)
- `offer_end_date`: When the offer ends (auto-set to 7 days if not provided)
- `offer_is_active`: Whether the offer is currently active

### 2. Automatic Expiration
- **Real-time checking**: Offers are validated when accessed
- **Cron job support**: Automated cleanup via management commands
- **Admin actions**: Bulk operations for offer management

### 3. API Endpoints
- `GET /api/products/on_offer/` - Get products currently on offer
- `GET /api/products/{id}/offer_details/` - Get offer details for a product
- `POST /api/products/{id}/toggle_offer/` - Toggle offer status (admin only)
- `POST /api/products/{id}/extend_offer/` - Extend offer duration (admin only)
- `GET /api/products/offer_stats/` - Get offer statistics

## How Offers Work

### 1. Creating an Offer

**Via Admin Interface:**
1. Go to Products in Django Admin
2. Edit a product
3. In "Offer Settings" section:
   - Check "Is on offer"
   - Set "Offer price" (must be less than regular price)
   - Optionally set start/end dates
4. Save the product

**Via API:**
```python
# Toggle offer for a product
POST /api/products/123/toggle_offer/
```

**Programmatically:**
```python
product = Product.objects.get(id=123)
product.is_on_offer = True
product.offer_price = product.price * 0.8  # 20% discount
product.save()  # Auto-sets dates if not provided
```

### 2. Offer Validation

The system automatically validates offers:
- Offer price must be less than regular price
- Expired offers are automatically deactivated
- Start/end dates are auto-set if not provided

### 3. Getting Current Price

```python
product = Product.objects.get(id=123)
current_price = product.get_current_price()  # Returns offer price if valid, else regular price
discount_percent = product.get_discount_percentage()
savings = product.get_savings_amount()
```

## Automatic Expiration

### 1. Cron Jobs (Recommended)

Set up cron jobs for automatic offer management:

```bash
# Every hour - deactivate expired offers
0 * * * * /path/to/mahbeauty/scripts/update_offers.sh hourly

# Daily at 9 AM - check expiring offers
0 9 * * * /path/to/mahbeauty/scripts/update_offers.sh daily

# Weekly cleanup
0 2 * * 0 /path/to/mahbeauty/scripts/update_offers.sh weekly
```

### 2. Manual Commands

```bash
# Check what would be updated
python manage.py update_expired_offers --dry-run

# Update expired offers
python manage.py update_expired_offers

# Clean up old expired offers
python manage.py update_expired_offers --cleanup-old
```

### 3. Real-time Middleware (Optional)

Add to `MIDDLEWARE` in settings.py:
```python
MIDDLEWARE = [
    # ... other middleware
    'products.middleware.OfferExpirationMiddleware',
]
```

## Admin Interface

### List View Features
- **Offer Status**: Shows current offer status with discount percentage
- **Bulk Actions**: Enable/disable offers, extend offers
- **Filters**: Filter by offer status, expiration dates

### Edit Form Features
- **Offer Settings**: Dedicated section for offer configuration
- **Offer Summary**: Real-time calculation of discounts and savings
- **Validation**: Automatic validation of offer prices and dates

## API Usage Examples

### Get Products on Offer
```javascript
fetch('/api/products/on_offer/')
  .then(response => response.json())
  .then(products => {
    products.forEach(product => {
      console.log(`${product.name}: ${product.discount_percentage}% off`);
    });
  });
```

### Get Offer Details
```javascript
fetch('/api/products/123/offer_details/')
  .then(response => response.json())
  .then(offer => {
    if (offer.is_offer_valid) {
      console.log(`Save $${offer.savings_amount}!`);
    }
  });
```

### Admin: Toggle Offer
```javascript
fetch('/api/products/123/toggle_offer/', {
  method: 'POST',
  headers: {
    'Authorization': 'Bearer your-admin-token',
    'Content-Type': 'application/json'
  }
})
.then(response => response.json())
.then(product => {
  console.log(`Offer ${product.is_on_offer ? 'enabled' : 'disabled'}`);
});
```

## Database Migration

To apply the changes, run:

```bash
python manage.py makemigrations products
python manage.py migrate
```

## Monitoring and Logs

### Log Files
- Cron job logs: `logs/offer_updates.log`
- Django logs: Check your Django logging configuration

### Health Checks
```bash
# Check recent offer updates
tail -20 logs/offer_updates.log

# Get offer statistics
curl /api/products/offer_stats/
```

## Best Practices

1. **Set Reasonable Expiration Dates**: Don't make offers too short or too long
2. **Monitor Logs**: Regularly check cron job logs for issues
3. **Use Bulk Actions**: Use admin bulk actions for managing multiple offers
4. **Test Before Production**: Always test with `--dry-run` first
5. **Set Up Monitoring**: Monitor offer statistics and expiration rates

## Troubleshooting

### Common Issues

1. **Offers Not Expiring**: Check cron job setup and logs
2. **Permission Errors**: Ensure cron jobs run with correct user permissions
3. **Database Locks**: If using high-frequency updates, consider optimizing queries

### Debug Commands

```bash
# Test offer expiration
python manage.py update_expired_offers --dry-run

# Check offer statistics
python manage.py shell -c "from products.models import Product; print(Product.objects.filter(is_on_offer=True).count())"

# Manual offer cleanup
python manage.py shell -c "from products.models import Product; print(Product.deactivate_expired_offers())"
```
