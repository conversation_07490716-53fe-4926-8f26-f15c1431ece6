#!/usr/bin/env python3
"""
Test script to verify email/password authentication endpoints are working
"""
import requests
import json

BASE_URL = "http://localhost:8000"

def test_user_registration():
    """Test user registration with email/password"""
    url = f"{BASE_URL}/auth/users/"
    data = {
        "email": "<EMAIL>",
        "password": "testpassword123",
        "re_password": "testpassword123",
        "first_name": "Test",
        "last_name": "User"
    }
    
    try:
        response = requests.post(url, json=data)
        print(f"Registration Status: {response.status_code}")
        print(f"Registration Response: {response.json()}")
        return response.status_code == 201
    except Exception as e:
        print(f"Registration Error: {e}")
        return False

def test_user_login():
    """Test user login with email/password"""
    url = f"{BASE_URL}/auth/jwt/create/"
    data = {
        "email": "<EMAIL>",
        "password": "testpassword123"
    }
    
    try:
        response = requests.post(url, json=data)
        print(f"Login Status: {response.status_code}")
        print(f"Login Response: {response.json()}")
        
        if response.status_code == 200:
            tokens = response.json()
            return tokens.get('access'), tokens.get('refresh')
        return None, None
    except Exception as e:
        print(f"Login Error: {e}")
        return None, None

def test_protected_endpoint(access_token):
    """Test accessing protected endpoint with JWT token"""
    url = f"{BASE_URL}/auth/users/me/"
    headers = {
        "Authorization": f"Bearer {access_token}"
    }
    
    try:
        response = requests.get(url, headers=headers)
        print(f"Profile Status: {response.status_code}")
        print(f"Profile Response: {response.json()}")
        return response.status_code == 200
    except Exception as e:
        print(f"Profile Error: {e}")
        return False

def main():
    print("Testing Email/Password Authentication Endpoints...")
    print("=" * 50)
    
    # Test registration
    print("\n1. Testing User Registration:")
    registration_success = test_user_registration()
    
    # Test login
    print("\n2. Testing User Login:")
    access_token, refresh_token = test_user_login()
    
    # Test protected endpoint
    if access_token:
        print("\n3. Testing Protected Endpoint:")
        profile_success = test_protected_endpoint(access_token)
    
    print("\n" + "=" * 50)
    print("Test Summary:")
    print(f"Registration: {'✅ PASS' if registration_success else '❌ FAIL'}")
    print(f"Login: {'✅ PASS' if access_token else '❌ FAIL'}")
    print(f"Protected Access: {'✅ PASS' if access_token and profile_success else '❌ FAIL'}")

if __name__ == "__main__":
    main()
