from django.db import models
from products.models import Product, Variation
from account.models import UserAccount

# Create your models here.


class Payment(models.Model):
    PAYMENT_METHODS = (
        ("Cash on Delivery", "Cash on Delivery"),
        ("ESewa", "ESewa"),
        ("<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"),
        ("Bank Transfer", "Bank Transfer"),
        ("Fonepay", "Fonepay"),
    )
    user = models.ForeignKey(UserAccount, on_delete=models.SET_NULL, null=True)
    payment_id = models.CharField(max_length=100)
    payment_method = models.CharField(
        choices=PAYMENT_METHODS, max_length=100, default="Cash on Delivery"
    )


class Order(models.Model):
    STATUS = (
        ("New", "New"),
        ("Accepted", "Accepted"),
        ("Completed", "Completed"),
        ("Cancelled", "Cancelled"),
    )

    user = models.ForeignKey(UserAccount, on_delete=models.SET_NULL, null=True)
    payment = models.ForeignKey(
        Payment, on_delete=models.SET_NULL, blank=True, null=True
    )
    order_number = models.CharField(max_length=20)
    first_name = models.CharField(max_length=50)
    last_name = models.CharField(max_length=50)
    phone = models.CharField(max_length=10)
    email = models.EmailField(max_length=50)
    state = models.CharField(max_length=250)
    area = models.CharField(max_length=250)
    address = models.CharField(max_length=250)
    status = models.CharField(max_length=10, choices=STATUS, default="New")
    ip = models.CharField(blank=True, max_length=20)
    grand_total = models.IntegerField(blank=True)
    tax = models.FloatField()
    is_ordered = models.BooleanField(default=False)
    order_note = models.TextField()
    created_at = models.DateTimeField(auto_now_add=True)
    upated_at = models.DateTimeField(auto_now=True)

    def full_name(self):
        return f"{self.first_name} {self.last_name}"

    def __str__(self):
        return self.first_name

    class Meta:
        verbose_name = "Customer Order"
        verbose_name_plural = "Customer Orders"


class OrderProduct(models.Model):
    user = models.ForeignKey(UserAccount, on_delete=models.CASCADE)
    payment = models.ForeignKey(
        Payment, on_delete=models.SET_NULL, null=True, blank=True
    )
    order = models.ForeignKey(Order, on_delete=models.CASCADE)
    product = models.ForeignKey(Product, on_delete=models.CASCADE)
    variation = models.ManyToManyField(Variation, blank=True)
    quantity = models.IntegerField()
    product_price = models.IntegerField()
    ordered = models.BooleanField(default=False)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return self.product.name
