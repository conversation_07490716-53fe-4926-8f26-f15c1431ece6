# Orders App API Documentation

This document describes the REST API endpoints for the orders app, including serializers and viewsets for managing payments, orders, and order products.

## 📋 Overview

The orders app provides the following API endpoints:
- **Payments**: Payment method and transaction management
- **Orders**: Customer order management
- **Order Products**: Individual items within orders

## 🔗 Base URL

All endpoints are prefixed with `/api/orders/`

## 📊 Models & Endpoints

### 1. Payment API

**Base URL**: `/api/orders/payments/`

#### Model Fields
- `id`: Auto-generated primary key
- `user`: Foreign key to UserAccount (nullable)
- `payment_id`: Unique payment identifier (string, max 100 chars)
- `payment_method`: Payment method choice (Cash on Delivery, ESewa, Khalti, Bank Transfer, Fonepay)

#### Endpoints

| Method | Endpoint | Description | Permission |
|--------|----------|-------------|------------|
| GET | `/api/orders/payments/` | List payments | Authenticated |
| POST | `/api/orders/payments/` | Create new payment | Authenticated |
| GET | `/api/orders/payments/{id}/` | Retrieve specific payment | Authenticated |
| PUT | `/api/orders/payments/{id}/` | Update payment | Authenticated |
| PATCH | `/api/orders/payments/{id}/` | Partial update payment | Authenticated |
| DELETE | `/api/orders/payments/{id}/` | Delete payment | Authenticated |

#### Custom Actions

| Method | Endpoint | Description |
|--------|----------|-------------|
| GET | `/api/orders/payments/{id}/orders/` | Get all orders for this payment |

#### Serializers

**PaymentSerializer** (Detail view)
```json
{
    "id": 1,
    "user": 1,
    "user_name": "John Doe",
    "payment_id": "PAY123456",
    "payment_method": "ESewa",
    "payment_method_display": "ESewa"
}
```

### 2. Order API

**Base URL**: `/api/orders/orders/`

#### Model Fields
- `id`: Auto-generated primary key
- `user`: Foreign key to UserAccount (nullable)
- `payment`: Foreign key to Payment (nullable)
- `order_number`: Unique order number (string, max 20 chars)
- `first_name`, `last_name`: Customer name
- `phone`: Phone number (10 digits)
- `email`: Customer email
- `state`, `area`, `address`: Shipping address
- `status`: Order status (New, Accepted, Completed, Cancelled)
- `grand_total`: Total amount
- `tax`: Tax amount
- `is_ordered`: Boolean flag
- `order_note`: Additional notes

#### Endpoints

| Method | Endpoint | Description | Permission |
|--------|----------|-------------|------------|
| GET | `/api/orders/orders/` | List orders | Authenticated |
| POST | `/api/orders/orders/` | Create new order | Authenticated |
| GET | `/api/orders/orders/{id}/` | Retrieve specific order | Authenticated |
| PUT | `/api/orders/orders/{id}/` | Update order | Authenticated |
| PATCH | `/api/orders/orders/{id}/` | Partial update order | Authenticated |
| DELETE | `/api/orders/orders/{id}/` | Delete order | Authenticated |

#### Custom Actions

| Method | Endpoint | Description |
|--------|----------|-------------|
| GET | `/api/orders/orders/{id}/items/` | Get all items in order |
| GET | `/api/orders/orders/{id}/summary/` | Get order summary with totals |
| POST | `/api/orders/orders/{id}/update_status/` | Update order status |
| POST | `/api/orders/orders/{id}/mark_ordered/` | Mark order as ordered |

#### Serializers

**OrderSerializer** (Detail view)
```json
{
    "id": 1,
    "user": {
        "id": 1,
        "email": "<EMAIL>",
        "first_name": "John",
        "last_name": "Doe",
        "full_name": "John Doe"
    },
    "payment": {
        "id": 1,
        "payment_id": "PAY123456",
        "payment_method": "ESewa"
    },
    "order_number": "ORD001",
    "first_name": "John",
    "last_name": "Doe",
    "full_name": "John Doe",
    "phone": "1234567890",
    "email": "<EMAIL>",
    "state": "Province 3",
    "area": "Kathmandu",
    "address": "123 Main St",
    "status": "New",
    "status_display": "New",
    "grand_total": 1500,
    "tax": 150.0,
    "is_ordered": false,
    "order_note": "Please deliver carefully",
    "created_at": "2024-01-01T12:00:00Z",
    "order_items_count": 2,
    "order_items": [...]
}
```

### 3. Order Products API

**Base URL**: `/api/orders/order-products/`

#### Model Fields
- `id`: Auto-generated primary key
- `user`: Foreign key to UserAccount
- `payment`: Foreign key to Payment (nullable)
- `order`: Foreign key to Order
- `product`: Foreign key to Product
- `variation`: Many-to-many with Variation
- `quantity`: Item quantity
- `product_price`: Price at time of order
- `ordered`: Boolean status

#### Endpoints

| Method | Endpoint | Description | Permission |
|--------|----------|-------------|------------|
| GET | `/api/orders/order-products/` | List order products | Authenticated |
| POST | `/api/orders/order-products/` | Add product to order | Authenticated |
| GET | `/api/orders/order-products/{id}/` | Retrieve specific item | Authenticated |
| PUT | `/api/orders/order-products/{id}/` | Update order item | Authenticated |
| PATCH | `/api/orders/order-products/{id}/` | Partial update item | Authenticated |
| DELETE | `/api/orders/order-products/{id}/` | Remove item from order | Authenticated |

#### Custom Actions

| Method | Endpoint | Description |
|--------|----------|-------------|
| POST | `/api/orders/order-products/{id}/update_quantity/` | Update item quantity |
| POST | `/api/orders/order-products/{id}/update_price/` | Update item price |
| POST | `/api/orders/order-products/{id}/toggle_ordered/` | Toggle ordered status |
| GET | `/api/orders/order-products/by_order/?order_id={id}` | Get items by order |
| GET | `/api/orders/order-products/by_product/?product_id={id}` | Get items by product |

## 🔍 Filtering & Search

### Orders Filtering
- **Search**: `order_number`, `first_name`, `last_name`, `email`, `phone`, `user__email`
- **Filters**: `status`, `is_ordered`, `payment__payment_method`, `user`
- **Ordering**: `created_at`, `grand_total`, `status`

### Order Products Filtering
- **Search**: `product__name`, `order__order_number`, `user__email`, `user__first_name`, `user__last_name`
- **Filters**: `ordered`, `product__category`, `order__status`, `payment__payment_method`, `user`
- **Ordering**: `created_at`, `quantity`, `product_price`

### Payments Filtering
- **Search**: `payment_id`, `user__email`, `user__first_name`, `user__last_name`
- **Filters**: `payment_method`, `user`
- **Ordering**: `payment_id`, `payment_method`

## 🔐 Permissions

- **Authenticated Users**: Can view and manage their own orders, payments, and order products
- **Staff Users**: Can view and manage all orders, payments, and order products

## 📝 Usage Examples

### Create a Payment
```bash
POST /api/orders/payments/
{
    "payment_id": "PAY123456",
    "payment_method": "ESewa"
}
```

### Create an Order
```bash
POST /api/orders/orders/
{
    "order_number": "ORD001",
    "first_name": "John",
    "last_name": "Doe",
    "phone": "1234567890",
    "email": "<EMAIL>",
    "state": "Province 3",
    "area": "Kathmandu",
    "address": "123 Main St",
    "grand_total": 1500,
    "tax": 150.0,
    "order_note": "Please deliver carefully",
    "payment_id": 1
}
```

### Add Product to Order
```bash
POST /api/orders/order-products/
{
    "product_id": 1,
    "order_id": 1,
    "variation_ids": [1],
    "quantity": 2,
    "product_price": 750
}
```

### Update Order Status
```bash
POST /api/orders/orders/1/update_status/
{
    "status": "Accepted"
}
```

## 🚨 Error Handling

The API returns appropriate HTTP status codes:
- `200`: Success
- `201`: Created
- `400`: Bad Request (validation errors)
- `401`: Unauthorized
- `403`: Forbidden
- `404`: Not Found
- `500`: Internal Server Error

Validation errors are returned in the following format:
```json
{
    "field_name": ["Error message"]
}
```
