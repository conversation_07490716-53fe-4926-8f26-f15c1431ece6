[project]
name = "mahbeauty"
version = "0.1.0"
description = "Add your description here"
readme = "README.md"
requires-python = ">=3.12"
dependencies = [
    "django>=5.2.4",
    "django-cors-headers>=4.7.0",
    "django-filter>=25.1",
    "django-jazzmin>=3.0.1",
    "django-jsonform>=2.23.2",
    "djangorestframework>=3.16.0",
    "djangorestframework-simplejwt>=5.5.0",
    "djoser>=2.3.3",
    "faker>=37.4.2",
    "google-auth>=2.40.3",
    "pillow>=11.3.0",
    "python-dotenv>=1.1.1",
    "requests>=2.32.4",
    "social-auth-app-django>=5.5.1",
    "whitenoise>=6.9.0",
]
