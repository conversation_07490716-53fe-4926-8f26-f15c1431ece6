JAZZMIN_SETTINGS = {
    "site_title": "MahBeauty",
    "site_header": "Mah<PERSON>eauty",
    "site_brand": "<PERSON>h<PERSON><PERSON><PERSON>",
    "welcome_sign": "Welcome to MahBeauty Admin",
    "copyright": "<PERSON><PERSON><PERSON><PERSON><PERSON>",
    "site_logo": "img/logo.png",
    "login_logo": "img/logo.png",

    # Menu ordering
    "order_with_respect_to": ["account","store", "products", "orders", "carts"],

    # Icons for apps and models
    "icons": {
        "account": "fas fa-users-cog",
        "account.UserAccount": "fas fa-user",
        "products": "fas fa-shopping-bag",
        "products.ProductCategory": "fas fa-tags",
        "products.Product": "fas fa-box",
        "products.Variation": "fas fa-palette",
        "products.ReviewRating": "fas fa-star",
        "orders": "fas fa-shopping-cart",
        "orders.Order": "fas fa-receipt",
        "orders.OrderProduct": "fas fa-list",
        "orders.Payment": "fas fa-credit-card",
        "carts": "fas fa-shopping-basket",
        "carts.Cart": "fas fa-shopping-basket",
        "carts.CartItem": "fas fa-plus-square",
        "store": "fas fa-store",
        "store.Organization": "fas fa-building",
        "store.HeroSlide": "fas fa-images",
        "store.HeroSlideImage": "fas fa-image",
        "store.AboutPageContent": "fas fa-info-circle",
        "store.Testimonial": "fas fa-quote-left",
    },

    # UI Settings
    "show_sidebar": True,
    "navigation_expanded": True,
    "changeform_format": "horizontal_tabs",
    "related_modal_active": False,
}

JAZZMIN_UI_TWEAKS = {
    "navbar_small_text": False,
    "footer_small_text": False,
    "body_small_text": False,
    "brand_small_text": False,
    "brand_colour": False,
    "accent": "accent-primary",
    "navbar": "navbar-dark",
    "no_navbar_border": False,
    "navbar_fixed": False,
    "layout_boxed": False,
    "footer_fixed": False,
    "sidebar_fixed": False,
    "sidebar": "sidebar-dark-primary",
    "sidebar_nav_small_text": False,
    "sidebar_disable_expand": False,
    "sidebar_nav_child_indent": False,
    "sidebar_nav_compact_style": False,
    "sidebar_nav_legacy_style": False,
    "sidebar_nav_flat_style": False,
    "theme": "default",
    "dark_mode_theme": None,
    "button_classes": {
        "primary": "btn-primary",
        "secondary": "btn-secondary",
        "info": "btn-info",
        "warning": "btn-warning",
        "danger": "btn-danger",
        "success": "btn-success"
    }
}