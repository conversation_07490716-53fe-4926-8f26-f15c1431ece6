from django.urls import path, include
from rest_framework.routers import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from rest_framework.decorators import api_view
from rest_framework.response import Response
from rest_framework.reverse import reverse
from account import views

# DRF Router
router = DefaultRouter()
router.register(r'users', views.UserAccountViewSet, basename='useraccount')

@api_view(['GET'])
def account_api_root(request, format=None):
    """
    API root for account app
    Note: This supplements <PERSON><PERSON><PERSON>'s authentication endpoints at /auth/
    """
    return Response({
        'users': reverse('useraccount-list', request=request, format=format),
        'my_profile': reverse('useraccount-me', request=request, format=format),
        'my_activity': reverse('useraccount-my-activity', request=request, format=format),
        'my_stats': reverse('useraccount-my-stats', request=request, format=format),
        'djoser_auth': request.build_absolute_uri('/api/auth/'),
    })

urlpatterns = [
    path('', account_api_root, name='account-api-root'),
    path('', include(router.urls)),
]
