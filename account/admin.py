from django.contrib import admin
from django.contrib.auth.admin import UserAdmin
from django.utils.html import format_html
from django.urls import reverse
from django.db.models import Count, Sum, Avg
from django.contrib.admin import SimpleListFilter
from django.utils import timezone
from .models import UserAccount


class UserTypeFilter(SimpleListFilter):
    title = 'User Type'
    parameter_name = 'user_type'

    def lookups(self, request, model_admin):
        return (
            ('google', 'Google Users'),
            ('regular', 'Regular Users'),
            ('staff', 'Staff Users'),
            ('superuser', 'Superusers'),
            ('active_customers', 'Active Customers'),
            ('inactive', 'Inactive Users'),
        )

    def queryset(self, request, queryset):
        if self.value() == 'google':
            return queryset.filter(google_id__isnull=False)
        elif self.value() == 'regular':
            return queryset.filter(google_id__isnull=True, is_staff=False, is_superuser=False)
        elif self.value() == 'staff':
            return queryset.filter(is_staff=True, is_superuser=False)
        elif self.value() == 'superuser':
            return queryset.filter(is_superuser=True)
        elif self.value() == 'active_customers':
            return queryset.filter(is_active=True, order__isnull=False).distinct()
        elif self.value() == 'inactive':
            return queryset.filter(is_active=False)


class ActivityFilter(SimpleListFilter):
    title = 'Activity Level'
    parameter_name = 'activity_level'

    def lookups(self, request, model_admin):
        return (
            ('high', 'High Activity (5+ orders)'),
            ('medium', 'Medium Activity (2-4 orders)'),
            ('low', 'Low Activity (1 order)'),
            ('no_orders', 'No Orders'),
        )

    def queryset(self, request, queryset):
        if self.value() == 'high':
            return queryset.annotate(order_count=Count('order')).filter(order_count__gte=5)
        elif self.value() == 'medium':
            return queryset.annotate(order_count=Count('order')).filter(order_count__range=(2, 4))
        elif self.value() == 'low':
            return queryset.annotate(order_count=Count('order')).filter(order_count=1)
        elif self.value() == 'no_orders':
            return queryset.annotate(order_count=Count('order')).filter(order_count=0)


@admin.register(UserAccount)
class UserAccountAdmin(UserAdmin):
    list_display = ('email_display', 'full_name_display', 'user_type_display', 'activity_summary',
                   'order_stats', 'account_status', 'is_active', 'last_login')
    list_filter = (UserTypeFilter, ActivityFilter, 'is_active', 'is_staff', 'is_superuser', 'last_login')
    search_fields = ('email', 'first_name', 'last_name', 'google_id')
    list_editable = ('is_active',)
    readonly_fields = ('last_login', 'user_summary', 'order_history',
                      'cart_summary', 'review_summary', 'testimonial_summary')
    ordering = ('-last_login',)
    list_per_page = 25

    fieldsets = (
        ('Account Information', {
            'fields': ('email', 'password')
        }),
        ('Personal Information', {
            'fields': ('first_name', 'last_name', 'google_id')
        }),
        ('Permissions', {
            'fields': ('is_active', 'is_staff', 'is_superuser', 'groups', 'user_permissions'),
            'classes': ('collapse',)
        }),
        ('Important Dates', {
            'fields': ('last_login',),
            'classes': ('collapse',)
        }),
        ('User Activity Summary', {
            'fields': ('user_summary',),
            'classes': ('collapse',)
        }),
        ('Order History', {
            'fields': ('order_history',),
            'classes': ('collapse',)
        }),
        ('Shopping Activity', {
            'fields': ('cart_summary', 'review_summary', 'testimonial_summary'),
            'classes': ('collapse',)
        }),
    )

    add_fieldsets = (
        ('Create New User', {
            'classes': ('wide',),
            'fields': ('email', 'first_name', 'last_name', 'password1', 'password2', 'is_active'),
        }),
    )

    actions = ['activate_users', 'deactivate_users', 'make_staff', 'remove_staff']

    def get_queryset(self, request):
        queryset = super().get_queryset(request)
        queryset = queryset.annotate(
            _order_count=Count('order', distinct=True),
            _total_spent=Sum('order__grand_total'),
            _cart_items=Count('cartitem', distinct=True),
            _reviews_count=Count('reviewrating', distinct=True),
            _testimonials_count=Count('testimonials', distinct=True),
            _avg_rating_given=Avg('reviewrating__rating')
        ).select_related().prefetch_related(
            'order_set', 'cartitem_set', 'reviewrating_set', 'testimonials'
        )
        return queryset

    def email_display(self, obj):
        if obj.google_id:
            return format_html('<span title="Google Account">🔗 {}</span>', obj.email)
        return format_html('<span>{}</span>', obj.email)
    email_display.short_description = 'Email'
    email_display.admin_order_field = 'email'

    def full_name_display(self, obj):
        full_name = obj.get_full_name()
        if full_name:
            return format_html('<strong>{}</strong>', full_name)
        return format_html('<span style="color: #999;">No name provided</span>')
    full_name_display.short_description = 'Full Name'
    full_name_display.admin_order_field = 'first_name'

    def user_type_display(self, obj):
        if obj.is_superuser:
            return format_html('<span style="color: #dc3545; font-weight: bold;">👑 Superuser</span>')
        elif obj.is_staff:
            return format_html('<span style="color: #fd7e14; font-weight: bold;">👨‍💼 Staff</span>')
        elif obj.google_id:
            return format_html('<span style="color: #007bff;">🔗 Google User</span>')
        else:
            return format_html('<span style="color: #28a745;">👤 Regular User</span>')
    user_type_display.short_description = 'Type'

    def activity_summary(self, obj):
        order_count = obj._order_count
        cart_items = obj._cart_items
        reviews = obj._reviews_count

        if order_count >= 5:
            activity_level = format_html('<span style="color: green; font-weight: bold;">🔥 High</span>')
        elif order_count >= 2:
            activity_level = format_html('<span style="color: orange; font-weight: bold;">📈 Medium</span>')
        elif order_count >= 1:
            activity_level = format_html('<span style="color: blue;">📊 Low</span>')
        else:
            activity_level = format_html('<span style="color: #999;">💤 Inactive</span>')

        return format_html('{}<br><small>{} orders, {} cart items, {} reviews</small>',
                          activity_level, order_count, cart_items, reviews)
    activity_summary.short_description = 'Activity'

    def order_stats(self, obj):
        order_count = obj._order_count
        total_spent = obj._total_spent or 0

        if order_count > 0:
            avg_order = total_spent / order_count
            url = reverse('admin:orders_order_changelist') + f'?user__id__exact={obj.id}'
            return format_html('<a href="{}">{} orders</a><br><small>Rs. {:,} total<br>Rs. {:,.0f} avg</small>',
                             url, order_count, total_spent, avg_order)
        return format_html('<span style="color: #999;">No orders</span>')
    order_stats.short_description = 'Orders'

    def account_status(self, obj):
        if not obj.is_active:
            return format_html('<span style="color: red; font-weight: bold;">❌ Inactive</span>')
        elif obj.last_login:
            days_since_login = (timezone.now() - obj.last_login).days
            if days_since_login <= 7:
                return format_html('<span style="color: green; font-weight: bold;">✅ Active</span>')
            elif days_since_login <= 30:
                return format_html('<span style="color: orange;">⚠️ Recent</span>')
            else:
                return format_html('<span style="color: red;">😴 Dormant</span>')
        else:
            return format_html('<span style="color: #999;">👻 Never logged in</span>')
    account_status.short_description = 'Status'
    account_status.admin_order_field = 'is_active'

    def user_summary(self, obj):
        summary_html = f'''
            <div style="background: #f8f9fa; padding: 15px; border-radius: 5px;">
                <h4>User Overview</h4>
                <div><strong>Full Name:</strong> {obj.get_full_name() or 'Not provided'}</div>
                <div><strong>Email:</strong> {obj.email}</div>
                <div><strong>Account Type:</strong>
                    {'Google Account' if obj.google_id else 'Regular Account'}
                    {' | Staff' if obj.is_staff else ''}
                    {' | Superuser' if obj.is_superuser else ''}
                </div>
                <div><strong>Status:</strong> {'Active' if obj.is_active else 'Inactive'}</div>
                <div><strong>Last Login:</strong> {obj.last_login.strftime('%B %d, %Y at %I:%M %p') if obj.last_login else 'Never'}</div>
                <div><strong>User ID:</strong> {obj.id}</div>
            </div>
        '''
        return format_html(summary_html)
    user_summary.short_description = 'User Summary'

    def order_history(self, obj):
        orders = obj.order_set.all()[:10]  # Show last 10 orders
        if not orders:
            return format_html('<div style="color: #999;">No orders placed</div>')

        history_html = '<div style="max-width: 600px;">'
        total_spent = 0
        for order in orders:
            status_color = {
                'New': '#007bff',
                'Accepted': '#28a745',
                'Completed': '#6f42c1',
                'Cancelled': '#dc3545'
            }.get(order.status, '#6c757d')

            total_spent += order.grand_total
            history_html += f'''
                <div style="padding: 8px; border-bottom: 1px solid #eee; display: flex; justify-content: space-between;">
                    <div>
                        <strong>{order.order_number}</strong>
                        <span style="color: {status_color}; font-weight: bold; margin-left: 10px;">[{order.status}]</span>
                        <br><small>{order.created_at.strftime('%b %d, %Y')}</small>
                    </div>
                    <div style="text-align: right;">
                        <strong>Rs. {order.grand_total:,}</strong>
                    </div>
                </div>
            '''

        if obj.order_set.count() > 10:
            history_html += f'<div style="padding: 8px; color: #999; text-align: center;">... and {obj.order_set.count() - 10} more orders</div>'

        history_html += f'''
            <div style="padding: 10px; background: #e9ecef; margin-top: 5px; border-radius: 3px; text-align: center;">
                <strong>Total Orders:</strong> {obj.order_set.count()} |
                <strong>Total Spent:</strong> Rs. {total_spent:,}
            </div>
        '''
        history_html += '</div>'
        return format_html(history_html)
    order_history.short_description = 'Order History'

    def cart_summary(self, obj):
        cart_items = obj.cartitem_set.filter(is_active=True)
        if not cart_items:
            return format_html('<div style="color: #999;">No active cart items</div>')

        cart_html = '<div style="max-width: 500px;">'
        total_value = 0
        for item in cart_items[:5]:  # Show first 5 items
            item_total = item.sub_total()
            total_value += item_total
            cart_html += f'''
                <div style="padding: 5px; border-bottom: 1px solid #eee;">
                    <strong>{item.product.name}</strong>
                    <span style="color: #666;">x{item.quantity}</span>
                    <span style="float: right;">Rs. {item_total:,.2f}</span>
                </div>
            '''

        if cart_items.count() > 5:
            cart_html += f'<div style="padding: 5px; color: #999;">... and {cart_items.count() - 5} more items</div>'

        cart_html += f'''
            <div style="padding: 8px; background: #e9ecef; margin-top: 5px; border-radius: 3px;">
                <strong>Total Items:</strong> {cart_items.count()} |
                <strong>Cart Value:</strong> Rs. {total_value:,.2f}
            </div>
        '''
        cart_html += '</div>'
        return format_html(cart_html)
    cart_summary.short_description = 'Current Cart'

    def review_summary(self, obj):
        reviews = obj.reviewrating_set.all()
        if not reviews:
            return format_html('<div style="color: #999;">No reviews written</div>')

        approved_reviews = reviews.filter(status=True)
        avg_rating = obj._avg_rating_given or 0

        summary_html = f'''
            <div style="background: #f8f9fa; padding: 10px; border-radius: 5px;">
                <div><strong>Total Reviews:</strong> {reviews.count()}</div>
                <div><strong>Approved:</strong> {approved_reviews.count()}</div>
                <div><strong>Average Rating Given:</strong>
                    {'★' * int(avg_rating)}{'☆' * (5 - int(avg_rating))} ({avg_rating:.1f}/5)
                </div>
                <div><strong>Latest Review:</strong> {reviews.first().created_at.strftime('%b %d, %Y') if reviews.exists() else 'None'}</div>
            </div>
        '''
        return format_html(summary_html)
    review_summary.short_description = 'Review Activity'

    def testimonial_summary(self, obj):
        testimonials = obj.testimonials.all()
        if not testimonials:
            return format_html('<div style="color: #999;">No testimonials</div>')

        approved_testimonials = testimonials.filter(is_approved=True)
        featured_testimonials = testimonials.filter(is_featured=True)

        summary_html = f'''
            <div style="background: #f8f9fa; padding: 10px; border-radius: 5px;">
                <div><strong>Total Testimonials:</strong> {testimonials.count()}</div>
                <div><strong>Approved:</strong> {approved_testimonials.count()}</div>
                <div><strong>Featured:</strong> {featured_testimonials.count()}</div>
                <div><strong>Latest:</strong> {testimonials.first().created_at.strftime('%b %d, %Y') if testimonials.exists() else 'None'}</div>
            </div>
        '''
        return format_html(summary_html)
    testimonial_summary.short_description = 'Testimonials'

    # Custom actions
    def activate_users(self, request, queryset):
        updated = queryset.update(is_active=True)
        self.message_user(request, f'{updated} users activated.')
    activate_users.short_description = "Activate selected users"

    def deactivate_users(self, request, queryset):
        updated = queryset.update(is_active=False)
        self.message_user(request, f'{updated} users deactivated.')
    deactivate_users.short_description = "Deactivate selected users"

    def make_staff(self, request, queryset):
        updated = queryset.update(is_staff=True)
        self.message_user(request, f'{updated} users granted staff access.')
    make_staff.short_description = "Grant staff access to selected users"

    def remove_staff(self, request, queryset):
        updated = queryset.filter(is_superuser=False).update(is_staff=False)
        self.message_user(request, f'{updated} users had staff access removed.')
    remove_staff.short_description = "Remove staff access from selected users"
