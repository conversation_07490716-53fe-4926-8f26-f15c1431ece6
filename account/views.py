from rest_framework import viewsets, status, permissions
from rest_framework.decorators import action
from rest_framework.response import Response
from rest_framework.filters import <PERSON><PERSON>ilter, OrderingFilter
from django_filters.rest_framework import DjangoFilterBackend
from django.db.models import Count, Sum, Avg, Q
from django.utils import timezone
from datetime import <PERSON><PERSON><PERSON>

from .models import UserAccount
from .serializers import (
    UserProfileSerializer, UserActivitySerializer, UserStatsSerializer, UserUpdateSerializer
)


class UserAccountViewSet(viewsets.ModelViewSet):
    """
    ViewSet for UserAccount model
    Provides extended user account management beyond Djoser
    """
    queryset = UserAccount.objects.all()
    serializer_class = UserProfileSerializer
    permission_classes = [permissions.IsAuthenticated]
    filter_backends = [SearchFilter, OrderingFilter, DjangoFilterBackend]
    search_fields = ['email', 'first_name', 'last_name']
    ordering_fields = ['email', 'first_name', 'last_name', 'last_login']
    ordering = ['-last_login']
    filterset_fields = ['is_active', 'is_staff']

    def get_queryset(self):
        """Filter users based on permissions"""
        queryset = self.queryset

        if self.request.user.is_staff:
            # Staff can see all users with activity annotations
            return queryset.annotate(
                _order_count=Count('order', distinct=True),
                _total_spent=Sum('order__grand_total'),
                _cart_items=Count('cartitem', filter=Q(cartitem__is_active=True), distinct=True),
                _reviews_count=Count('reviewrating', distinct=True),
                _testimonials_count=Count('testimonials', distinct=True),
                _avg_rating_given=Avg('reviewrating__rating')
            ).select_related().prefetch_related(
                'order_set', 'cartitem_set', 'reviewrating_set', 'testimonials'
            )
        else:
            # Regular users can only see their own profile
            return queryset.filter(id=self.request.user.id)

    def get_serializer_class(self):
        """Return appropriate serializer based on action"""
        if self.action in ['update', 'partial_update']:
            return UserUpdateSerializer
        elif self.action == 'activity':
            return UserActivitySerializer
        return UserProfileSerializer

    @action(detail=True, methods=['get'])
    def activity(self, request, pk=None):
        """Get user activity summary"""
        user = self.get_object()

        # Check permission - users can only see their own activity unless staff
        if not request.user.is_staff and user != request.user:
            return Response(
                {'error': 'Permission denied'},
                status=status.HTTP_403_FORBIDDEN
            )

        # Annotate user with activity data
        user_with_stats = UserAccount.objects.annotate(
            _order_count=Count('order', distinct=True),
            _total_spent=Sum('order__grand_total'),
            _cart_items=Count('cartitem', filter=Q(cartitem__is_active=True), distinct=True),
            _reviews_count=Count('reviewrating', distinct=True),
            _testimonials_count=Count('testimonials', distinct=True),
            _avg_rating_given=Avg('reviewrating__rating')
        ).get(id=user.id)

        serializer = UserActivitySerializer(user_with_stats, context={'request': request})
        return Response(serializer.data)

    @action(detail=True, methods=['get'])
    def stats(self, request, pk=None):
        """Get detailed user statistics"""
        user = self.get_object()

        # Check permission
        if not request.user.is_staff and user != request.user:
            return Response(
                {'error': 'Permission denied'},
                status=status.HTTP_403_FORBIDDEN
            )

        # Calculate detailed statistics
        now = timezone.now()
        thirty_days_ago = now - timedelta(days=30)

        # Order statistics
        orders = user.order_set.all()
        order_stats = {
            'total_orders': orders.count(),
            'completed_orders': orders.filter(status='Completed').count(),
            'pending_orders': orders.filter(status__in=['New', 'Accepted']).count(),
            'cancelled_orders': orders.filter(status='Cancelled').count(),
            'total_spent': orders.aggregate(total=Sum('grand_total'))['total'] or 0,
            'recent_orders': orders.filter(created_at__gte=thirty_days_ago).count(),
            'average_order_value': orders.aggregate(avg=Avg('grand_total'))['avg'] or 0
        }

        # Review statistics
        reviews = user.reviewrating_set.all()
        review_stats = {
            'total_reviews': reviews.count(),
            'approved_reviews': reviews.filter(status=True).count(),
            'pending_reviews': reviews.filter(status=False).count(),
            'average_rating_given': reviews.aggregate(avg=Avg('rating'))['avg'] or 0,
            'recent_reviews': reviews.filter(created_at__gte=thirty_days_ago).count()
        }

        # Cart statistics
        cart_items = user.cartitem_set.filter(is_active=True)
        cart_stats = {
            'active_cart_items': cart_items.count(),
            'cart_total_value': sum(item.sub_total() for item in cart_items),
            'unique_products_in_cart': cart_items.values('product').distinct().count()
        }

        # Testimonial statistics
        testimonials = user.testimonials.all()
        testimonial_stats = {
            'total_testimonials': testimonials.count(),
            'approved_testimonials': testimonials.filter(is_approved=True).count(),
            'featured_testimonials': testimonials.filter(is_featured=True).count(),
            'average_testimonial_rating': testimonials.aggregate(avg=Avg('rating'))['avg'] or 0
        }

        stats_data = {
            'orders': order_stats,
            'reviews': review_stats,
            'cart': cart_stats,
            'testimonials': testimonial_stats
        }

        serializer = UserStatsSerializer(stats_data)
        return Response(serializer.data)

    @action(detail=True, methods=['get'])
    def orders(self, request, pk=None):
        """Get user's orders"""
        user = self.get_object()

        # Check permission
        if not request.user.is_staff and user != request.user:
            return Response(
                {'error': 'Permission denied'},
                status=status.HTTP_403_FORBIDDEN
            )

        from orders.serializers import OrderListSerializer
        orders = user.order_set.select_related('payment').all()

        # Apply filters
        status_filter = request.query_params.get('status')
        if status_filter:
            orders = orders.filter(status=status_filter)

        serializer = OrderListSerializer(orders, many=True, context={'request': request})
        return Response(serializer.data)

    @action(detail=True, methods=['get'])
    def reviews(self, request, pk=None):
        """Get user's reviews"""
        user = self.get_object()

        # Check permission
        if not request.user.is_staff and user != request.user:
            return Response(
                {'error': 'Permission denied'},
                status=status.HTTP_403_FORBIDDEN
            )

        from products.serializers import ReviewRatingListSerializer
        reviews = user.reviewrating_set.select_related('product').all()

        # Apply filters
        status_filter = request.query_params.get('status')
        if status_filter is not None:
            reviews = reviews.filter(status=status_filter.lower() == 'true')

        serializer = ReviewRatingListSerializer(reviews, many=True, context={'request': request})
        return Response(serializer.data)

    @action(detail=True, methods=['get'])
    def cart(self, request, pk=None):
        """Get user's current cart items"""
        user = self.get_object()

        # Check permission
        if not request.user.is_staff and user != request.user:
            return Response(
                {'error': 'Permission denied'},
                status=status.HTTP_403_FORBIDDEN
            )

        from carts.serializers import CartItemListSerializer
        cart_items = user.cartitem_set.filter(is_active=True).select_related(
            'product', 'cart'
        ).prefetch_related('variation')

        serializer = CartItemListSerializer(cart_items, many=True, context={'request': request})
        return Response(serializer.data)

    @action(detail=False, methods=['get'])
    def me(self, request):
        """Get current user's profile (alternative to Djoser's /auth/users/me/)"""
        serializer = self.get_serializer(request.user)
        return Response(serializer.data)

    @action(detail=False, methods=['get'])
    def my_activity(self, request):
        """Get current user's activity summary"""
        return self.activity(request, pk=request.user.id)

    @action(detail=False, methods=['get'])
    def my_stats(self, request):
        """Get current user's detailed statistics"""
        return self.stats(request, pk=request.user.id)
