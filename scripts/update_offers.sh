#!/bin/bash

# Script to update expired offers - designed for cron jobs
# Usage: ./scripts/update_offers.sh [options]

# Set the project directory (adjust this path as needed)
PROJECT_DIR="/home/<USER>/Projects/mahbeauty"
PYTHON_PATH="$PROJECT_DIR/.venv/bin/python"
MANAGE_PY="$PROJECT_DIR/manage.py"
LOG_DIR="$PROJECT_DIR/logs"
LOG_FILE="$LOG_DIR/offer_updates.log"

# Create logs directory if it doesn't exist
mkdir -p "$LOG_DIR"

# Function to log with timestamp
log_with_timestamp() {
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] $1" >> "$LOG_FILE"
}

# Function to run the management command
run_offer_update() {
    local args="$1"
    
    log_with_timestamp "Starting offer update with args: $args"
    
    cd "$PROJECT_DIR"
    
    # Run the Django management command
    if $PYTHON_PATH "$MANAGE_PY" update_expired_offers $args >> "$LOG_FILE" 2>&1; then
        log_with_timestamp "Offer update completed successfully"
        exit 0
    else
        log_with_timestamp "Offer update failed with exit code $?"
        exit 1
    fi
}

# Parse command line arguments
case "$1" in
    "hourly")
        # Run every hour - just deactivate expired offers
        run_offer_update "--quiet"
        ;;
    "daily")
        # Run daily - check expiring offers and log warnings
        run_offer_update "--notify-expiring 24"
        ;;
    "weekly")
        # Run weekly - clean up old expired offers
        run_offer_update "--cleanup-old --notify-expiring 168"
        ;;
    "dry-run")
        # Test run - show what would happen
        run_offer_update "--dry-run"
        ;;
    *)
        echo "Usage: $0 {hourly|daily|weekly|dry-run}"
        echo ""
        echo "  hourly  - Deactivate expired offers (quiet mode)"
        echo "  daily   - Check for offers expiring in 24 hours"
        echo "  weekly  - Clean up old expired offers and check for offers expiring in 7 days"
        echo "  dry-run - Show what would be updated without making changes"
        echo ""
        echo "Log file: $LOG_FILE"
        exit 1
        ;;
esac
