# MahBeauty Fake Data Generator

A comprehensive script to generate realistic test data for the MahBeauty Django application using Faker.

## 🎯 Purpose

This script generates realistic test data for all models in your MahBeauty application, including:
- User accounts with various roles
- Beauty product categories and products
- Product variations and reviews
- Orders and payments
- Shopping carts and cart items
- Store information and content
- Testimonials and hero slides

## 📋 Prerequisites

1. **Python Environment**: Ensure you have Python 3.8+ installed
2. **Django Project**: Your MahBeauty Django project should be properly set up
3. **Database**: Your database should be migrated and ready

## 🚀 Installation

1. **Install additional dependencies**:
   ```bash
   pip install -r fake_data_requirements.txt
   ```
   
   Or install manually:
   ```bash
   pip install faker pillow
   ```

## 💻 Usage

### Basic Usage

Run the script from your project root directory:

```bash
python generate_fake_data.py
```

### Interactive Options

When you run the script, you'll be prompted:
- **Clear existing data**: Choose whether to delete existing data before generating new data

### What Gets Generated

#### User Accounts (50 users)
- Regular users with realistic names and emails
- Staff users (first 5 users)
- Superusers (first 2 users)
- Some users with Google authentication
- Mix of active/inactive accounts

#### Product Data
- **8 Categories**: Skincare, Makeup, Haircare, Fragrance, Body Care, Nail Care, Men's Grooming, Tools & Accessories
- **200 Products**: Realistic beauty products with:
  - Brand names and product descriptions
  - Prices in NPR (10-500 range)
  - Beauty-specific ingredients (JSON format)
  - Usage instructions and benefits
  - Product images (generated)
  - Offer pricing and dates
  - Stock status

#### Product Variations (300 items)
- Color variations for products
- Realistic color names (Fair, Light, Medium, etc.)

#### Reviews & Ratings (500 reviews)
- User reviews for products
- Ratings skewed towards positive (4-5 stars)
- Realistic review subjects and content
- Approval status

#### Orders & Payments (150 orders, 100 payments)
- **Payment Methods**: Cash on Delivery, ESewa, Khalti, Bank Transfer, Fonepay
- **Order Details**: 
  - Nepal-specific addresses (states and areas)
  - Realistic phone numbers (Nepal format)
  - Order totals with 13% VAT
  - Various order statuses

#### Order Products (400 items)
- Products within orders
- Quantities and pricing
- Product variations

#### Shopping Carts (30 carts, 100 items)
- Active shopping carts
- Cart items with quantities
- Product variations

#### Store Content
- **Organization**: Company information with social media links
- **Hero Slides**: 5 promotional slides with images
- **About Page Content**: Story, Mission, Vision, Values, Header sections
- **Testimonials**: 50 customer testimonials with ratings

## 🎨 Beauty-Specific Features

### Realistic Product Data
- **Categories**: Industry-standard beauty categories
- **Products**: Real beauty product types (serums, foundations, shampoos, etc.)
- **Ingredients**: Common beauty ingredients (Hyaluronic Acid, Vitamin C, Retinol, etc.)
- **Benefits**: Realistic product benefits
- **Colors**: Beauty-appropriate color variations

### Nepal-Specific Data
- **States**: 7 provinces of Nepal
- **Areas**: Major cities and districts
- **Phone Numbers**: Nepal mobile format (98XXXXXXXX)
- **Currency**: Prices in NPR

## 📊 Generated Data Summary

After running the script, you'll get:

```
Users: 50
Categories: 8
Products: 200
Variations: 300
Reviews: 500
Payments: 100
Orders: 150
Order Products: 400
Carts: 30
Cart Items: 100
Organization: 1
Hero Slides: 5
About Contents: 5
Testimonials: 50
```

## ⚠️ Important Notes

### Data Relationships
- All foreign key relationships are properly maintained
- Users are linked to their orders, reviews, and testimonials
- Products are linked to categories, variations, and reviews
- Orders contain realistic product combinations

### Image Generation
- Product images are generated as colored rectangles
- Hero slide images are created automatically
- Organization logo is generated
- All images are saved in appropriate upload directories

### Data Quality
- Email addresses are unique
- Phone numbers follow Nepal format
- Prices are realistic for beauty products
- Ratings are skewed towards positive reviews
- Most items are active/approved by default

## 🔧 Customization

You can modify the script to:
- Change the number of items generated
- Add new product categories or types
- Modify price ranges
- Add new beauty ingredients or benefits
- Change geographical data for other countries

## 🚨 Safety Features

- Preserves superuser accounts when clearing data
- Generates data in proper dependency order
- Handles unique constraints properly
- Provides detailed progress feedback

## 🐛 Troubleshooting

### Common Issues

1. **Import Errors**: Ensure Django environment is properly set up
2. **Database Errors**: Make sure migrations are applied
3. **Permission Errors**: Check file system permissions for image uploads
4. **Memory Issues**: Reduce the count parameters if generating too much data

### Error Messages

The script provides detailed error messages and will exit gracefully if issues occur.

## 📝 Example Output

```
============================================================
MahBeauty Fake Data Generator
============================================================
Do you want to clear existing data? (y/N): y
Clearing existing data...
Existing data cleared!

Starting data generation...
Generating 50 users...
Created 50 users
Generating product categories...
Created 8 categories
Generating 200 products...
Created 200 products
...
============================================================
DATA GENERATION COMPLETE!
============================================================
Total time taken: 0:02:15.123456

You can now test your application with realistic data!
```

## 🎉 Next Steps

After generating the data:
1. Start your Django development server
2. Visit the admin panel to see the generated data
3. Test your API endpoints with realistic data
4. Use the data for frontend development and testing

The generated data provides a comprehensive foundation for testing all aspects of your MahBeauty application!
