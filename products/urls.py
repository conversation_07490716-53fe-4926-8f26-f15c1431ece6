from django.urls import path, include
from rest_framework.routers import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from rest_framework.decorators import api_view
from rest_framework.response import Response
from rest_framework.reverse import reverse
from products import views

# DRF Router
router = DefaultRouter()
router.register(r'categories', views.ProductCategoryViewSet, basename='productcategory')
router.register(r'products', views.ProductViewSet, basename='product')
router.register(r'variations', views.VariationViewSet, basename='variation')
router.register(r'reviews', views.ReviewRatingViewSet, basename='reviewrating')

@api_view(['GET'])
def products_api_root(request, format=None):
    """
    API root for products app
    """
    return Response({
        'categories': reverse('productcategory-list', request=request, format=format),
        'products': reverse('product-list', request=request, format=format),
        'variations': reverse('variation-list', request=request, format=format),
        'reviews': reverse('reviewrating-list', request=request, format=format),
    })

urlpatterns = [
    path('', products_api_root, name='products-api-root'),
    path('', include(router.urls)),
]
