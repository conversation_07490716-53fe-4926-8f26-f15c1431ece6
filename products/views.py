from rest_framework import viewsets, status, permissions
from rest_framework.decorators import action
from rest_framework.response import Response
from rest_framework.filters import <PERSON>Filter, OrderingFilter
from django_filters.rest_framework import DjangoFilterBackend
from django.db.models import Count, Avg, Q
from django.db import models
from django.utils import timezone

from .models import ProductCategory, Product, Variation, ReviewRating
from .serializers import (
    ProductCategorySerializer, ProductCategoryListSerializer,
    ProductSerializer, ProductListSerializer, ProductCreateSerializer, ProductBasicSerializer,
    VariationSerializer, VariationListSerializer, VariationCreateSerializer,
    ReviewRatingSerializer, ReviewRatingListSerializer, ReviewRatingCreateSerializer
)


class ProductCategoryViewSet(viewsets.ModelViewSet):
    """
    ViewSet for ProductCategory model
    Provides CRUD operations for product categories
    """
    queryset = ProductCategory.objects.all()
    serializer_class = ProductCategorySerializer
    permission_classes = [permissions.IsAuthenticatedOrReadOnly]
    filter_backends = [SearchFilter, OrderingFilter]
    search_fields = ['name']
    ordering_fields = ['name']
    ordering = ['name']

    def get_serializer_class(self):
        """Return appropriate serializer based on action"""
        if self.action == 'list':
            return ProductCategoryListSerializer
        return ProductCategorySerializer

    def get_queryset(self):
        """Optimize queryset with annotations"""
        return ProductCategory.objects.annotate(
            _product_count=Count('products', filter=Q(products__is_active=True)),
            _avg_rating=Avg('products__rating', filter=Q(products__is_active=True))
        )

    @action(detail=True, methods=['get'])
    def products(self, request, pk=None):
        """Get all products in this category"""
        category = self.get_object()
        products = Product.objects.filter(category=category, is_active=True).select_related('category')

        # Apply filters
        in_stock = request.query_params.get('in_stock')
        if in_stock is not None:
            products = products.filter(in_stock=in_stock.lower() == 'true')

        min_price = request.query_params.get('min_price')
        if min_price:
            try:
                products = products.filter(price__gte=float(min_price))
            except ValueError:
                pass

        max_price = request.query_params.get('max_price')
        if max_price:
            try:
                products = products.filter(price__lte=float(max_price))
            except ValueError:
                pass

        serializer = ProductListSerializer(products, many=True, context={'request': request})
        return Response(serializer.data)

    @action(detail=False, methods=['get'])
    def with_products(self, request):
        """Get categories that have active products"""
        categories = self.get_queryset().filter(_product_count__gt=0)
        serializer = self.get_serializer(categories, many=True)
        return Response(serializer.data)


class ProductViewSet(viewsets.ModelViewSet):
    """
    ViewSet for Product model
    Provides CRUD operations for products
    """
    queryset = Product.objects.select_related('category').prefetch_related(
        'variation_set', 'offerproducts_set', 'reviewrating_set__user'
    )
    serializer_class = ProductSerializer
    permission_classes = [permissions.IsAuthenticatedOrReadOnly]
    filter_backends = [SearchFilter, OrderingFilter, DjangoFilterBackend]
    search_fields = ['name', 'description', 'category__name']
    ordering_fields = ['name', 'price', 'rating', 'created_at']
    ordering = ['-created_at']
    filterset_fields = ['category', 'in_stock', 'is_active']

    def get_serializer_class(self):
        """Return appropriate serializer based on action"""
        if self.action == 'list':
            return ProductListSerializer
        elif self.action == 'create':
            return ProductCreateSerializer
        return ProductSerializer

    def get_queryset(self):
        """Filter products and optimize queries"""
        queryset = self.queryset

        # Filter by active status for non-staff users
        if not (self.request.user.is_authenticated and self.request.user.is_staff):
            queryset = queryset.filter(is_active=True)

        # Add annotations for list view optimization
        if self.action == 'list':
            queryset = queryset.annotate(
                _review_count=Count('reviewrating'),
                _avg_rating=Avg('reviewrating__rating')
            )

        return queryset

    @action(detail=True, methods=['get'])
    def variations(self, request, pk=None):
        """Get all variations for this product"""
        product = self.get_object()
        variations = Variation.objects.filter(product=product, is_active=True)

        serializer = VariationListSerializer(variations, many=True, context={'request': request})
        return Response(serializer.data)

    @action(detail=True, methods=['get'])
    def offer_details(self, request, pk=None):
        """Get offer details for this product"""
        product = self.get_object()

        offer_data = {
            'is_on_offer': product.is_on_offer,
            'is_offer_valid': product.is_offer_valid(),
            'original_price': product.price,
            'offer_price': product.offer_price,
            'current_price': product.get_current_price(),
            'discount_percentage': product.get_discount_percentage(),
            'savings_amount': product.get_savings_amount(),
            'offer_start_date': product.offer_start_date,
            'offer_end_date': product.offer_end_date,
            'offer_is_active': product.offer_is_active
        }

        return Response(offer_data)

    @action(detail=True, methods=['get'])
    def reviews(self, request, pk=None):
        """Get all reviews for this product"""
        product = self.get_object()
        reviews = ReviewRating.objects.filter(product=product).select_related('user')

        serializer = ReviewRatingListSerializer(reviews, many=True, context={'request': request})
        return Response(serializer.data)

    @action(detail=False, methods=['get'])
    def on_offer(self, request):
        """Get products currently on offer"""
        # First, deactivate any expired offers
        Product.deactivate_expired_offers()

        offer_products = self.get_queryset().filter(
            is_on_offer=True,
            offer_is_active=True,
            is_active=True
        )

        # Filter by valid offers only
        from django.utils import timezone
        now = timezone.now()
        offer_products = offer_products.filter(
            models.Q(offer_start_date__isnull=True) | models.Q(offer_start_date__lte=now),
            models.Q(offer_end_date__isnull=True) | models.Q(offer_end_date__gt=now)
        )

        serializer = ProductListSerializer(offer_products, many=True, context={'request': request})
        return Response(serializer.data)

    @action(detail=True, methods=['post'])
    def toggle_offer(self, request, pk=None):
        """Toggle offer status for a product"""
        product = self.get_object()

        if not request.user.is_staff:
            return Response(
                {'error': 'Permission denied'},
                status=status.HTTP_403_FORBIDDEN
            )

        product.is_on_offer = not product.is_on_offer

        # If enabling offer, set default values if not present
        if product.is_on_offer:
            if not product.offer_price:
                product.offer_price = product.price * 0.9  # 10% discount
            if not product.offer_start_date:
                product.offer_start_date = timezone.now()
            if not product.offer_end_date:
                product.offer_end_date = timezone.now() + timezone.timedelta(days=7)
            product.offer_is_active = True

        product.save()

        serializer = ProductSerializer(product, context={'request': request})
        return Response(serializer.data)

    @action(detail=True, methods=['post'])
    def extend_offer(self, request, pk=None):
        """Extend offer duration for a product"""
        product = self.get_object()

        if not request.user.is_staff:
            return Response(
                {'error': 'Permission denied'},
                status=status.HTTP_403_FORBIDDEN
            )

        if not product.is_on_offer:
            return Response(
                {'error': 'Product is not on offer'},
                status=status.HTTP_400_BAD_REQUEST
            )

        days = request.data.get('days', 7)
        try:
            days = int(days)
            if days < 1:
                raise ValueError()
        except ValueError:
            return Response(
                {'error': 'Days must be a positive integer'},
                status=status.HTTP_400_BAD_REQUEST
            )

        from datetime import timedelta
        if product.offer_end_date:
            product.offer_end_date += timedelta(days=days)
        else:
            product.offer_end_date = timezone.now() + timedelta(days=days)

        product.offer_is_active = True
        product.save()

        serializer = ProductSerializer(product, context={'request': request})
        return Response(serializer.data)

    @action(detail=False, methods=['get'])
    def featured(self, request):
        """Get featured products (high rating, in stock)"""
        featured_products = self.get_queryset().filter(
            rating__gte=4.0,
            in_stock=True,
            is_active=True
        ).order_by('-rating', '-created_at')[:10]

        serializer = ProductListSerializer(featured_products, many=True, context={'request': request})
        return Response(serializer.data)

    @action(detail=False, methods=['post'])
    def update_expired_offers(self, request):
        """Manually trigger expired offers update (admin only)"""
        if not request.user.is_staff:
            return Response(
                {'error': 'Permission denied'},
                status=status.HTTP_403_FORBIDDEN
            )

        expired_count = Product.deactivate_expired_offers()

        return Response({
            'message': f'{expired_count} expired offers deactivated',
            'expired_count': expired_count
        })

    @action(detail=False, methods=['get'])
    def offer_stats(self, request):
        """Get offer statistics"""
        now = timezone.now()

        stats = {
            'total_products_on_offer': Product.objects.filter(is_on_offer=True).count(),
            'active_offers': Product.objects.filter(
                is_on_offer=True,
                offer_is_active=True,
                offer_end_date__gt=now
            ).count(),
            'expired_offers': Product.objects.filter(
                is_on_offer=True,
                offer_end_date__lte=now
            ).count(),
            'expiring_soon': Product.objects.filter(
                is_on_offer=True,
                offer_is_active=True,
                offer_end_date__gt=now,
                offer_end_date__lte=now + timezone.timedelta(hours=24)
            ).count()
        }

        return Response(stats)

    @action(detail=False, methods=['get'])
    def on_sale(self, request):
        """Get products currently on sale"""
        products_with_offers = Product.objects.filter(
            offerproducts__is_active=True,
            offerproducts__end_date__gt=timezone.now(),
            is_active=True
        ).distinct().select_related('category')

        serializer = ProductListSerializer(products_with_offers, many=True, context={'request': request})
        return Response(serializer.data)


class VariationViewSet(viewsets.ModelViewSet):
    """
    ViewSet for Variation model
    Provides CRUD operations for product variations
    """
    queryset = Variation.objects.select_related('product')
    serializer_class = VariationSerializer
    permission_classes = [permissions.IsAuthenticatedOrReadOnly]
    filter_backends = [SearchFilter, OrderingFilter, DjangoFilterBackend]
    search_fields = ['product__name', 'variation_value']
    ordering_fields = ['created_date', 'variation_category', 'variation_value']
    ordering = ['-created_date']
    filterset_fields = ['variation_category', 'is_active', 'product']

    def get_serializer_class(self):
        """Return appropriate serializer based on action"""
        if self.action == 'list':
            return VariationListSerializer
        elif self.action == 'create':
            return VariationCreateSerializer
        return VariationSerializer

    def get_queryset(self):
        """Filter variations based on permissions"""
        queryset = self.queryset

        # Filter by active status for non-staff users
        if not (self.request.user.is_authenticated and self.request.user.is_staff):
            queryset = queryset.filter(is_active=True, product__is_active=True)

        return queryset

    @action(detail=True, methods=['post'])
    def toggle_active(self, request, pk=None):
        """Toggle active status of variation"""
        variation = self.get_object()
        variation.is_active = not variation.is_active
        variation.save()

        serializer = self.get_serializer(variation)
        return Response(serializer.data)

    @action(detail=False, methods=['get'])
    def by_category(self, request):
        """Get variations by category"""
        category = request.query_params.get('category')
        if not category:
            return Response(
                {'error': 'category parameter is required'},
                status=status.HTTP_400_BAD_REQUEST
            )

        variations = self.get_queryset().filter(variation_category=category)
        serializer = self.get_serializer(variations, many=True)
        return Response(serializer.data)





class ReviewRatingViewSet(viewsets.ModelViewSet):
    """
    ViewSet for ReviewRating model
    Provides CRUD operations for product reviews
    """
    queryset = ReviewRating.objects.select_related('product', 'user')
    serializer_class = ReviewRatingSerializer
    permission_classes = [permissions.IsAuthenticated]
    filter_backends = [SearchFilter, OrderingFilter, DjangoFilterBackend]
    search_fields = ['product__name', 'subject', 'review', 'user__email']
    ordering_fields = ['created_at', 'rating']
    ordering = ['-created_at']
    filterset_fields = ['rating', 'product', 'user']

    def get_serializer_class(self):
        """Return appropriate serializer based on action"""
        if self.action == 'list':
            return ReviewRatingListSerializer
        elif self.action == 'create':
            return ReviewRatingCreateSerializer
        return ReviewRatingSerializer

    def get_queryset(self):
        """Filter reviews based on user permissions"""
        queryset = self.queryset

        if self.request.user.is_staff:
            return queryset  # Staff can see all reviews
        else:
            # Regular users can see all reviews and their own reviews
            return queryset

    def perform_create(self, serializer):
        """Set user and IP when creating review, and update product rating"""
        # Get client IP
        ip = self.request.META.get('REMOTE_ADDR', '')
        review = serializer.save(user=self.request.user, ip=ip)

        # Update product rating when new review is created
        product = review.product
        all_reviews = ReviewRating.objects.filter(product=product)
        if all_reviews.exists():
            avg_rating = sum(r.rating for r in all_reviews) / all_reviews.count()
            product.rating = round(avg_rating, 2)
            product.save()

    @action(detail=False, methods=['get'])
    def my_reviews(self, request):
        """Get current user's reviews"""
        reviews = self.get_queryset().filter(user=request.user)
        serializer = self.get_serializer(reviews, many=True)
        return Response(serializer.data)



    @action(detail=False, methods=['get'])
    def by_rating(self, request):
        """Get reviews by rating"""
        rating = request.query_params.get('rating')
        if not rating:
            return Response(
                {'error': 'rating parameter is required'},
                status=status.HTTP_400_BAD_REQUEST
            )

        try:
            rating = float(rating)
            if rating < 0 or rating > 5:
                raise ValueError()
        except ValueError:
            return Response(
                {'error': 'Rating must be a number between 0 and 5'},
                status=status.HTTP_400_BAD_REQUEST
            )

        reviews = self.get_queryset().filter(rating=rating)
        serializer = self.get_serializer(reviews, many=True)
        return Response(serializer.data)

    @action(detail=False, methods=['get'])
    def product_reviews(self, request):
        """Get reviews for a specific product"""
        product_id = request.query_params.get('product_id')
        if not product_id:
            return Response(
                {'error': 'product_id parameter is required'},
                status=status.HTTP_400_BAD_REQUEST
            )

        try:
            product_id = int(product_id)
        except ValueError:
            return Response(
                {'error': 'product_id must be a valid integer'},
                status=status.HTTP_400_BAD_REQUEST
            )

        reviews = self.get_queryset().filter(product_id=product_id)
        serializer = self.get_serializer(reviews, many=True)
        return Response(serializer.data)
