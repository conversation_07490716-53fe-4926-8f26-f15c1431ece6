"""
Django management command to generate fake data for MahBeauty application.

Usage:
    python manage.py generate_fake_data
    python manage.py generate_fake_data --clear
    python manage.py generate_fake_data --users 100 --products 500
"""

from django.core.management.base import BaseCommand, CommandError
from django.utils import timezone
from decimal import Decimal
import random
from io import BytesIO
from PIL import Image
from django.core.files.base import ContentFile

# Import the main generation functions from the standalone script
import sys
import os

# Add the project root to the path so we can import the standalone script
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(__file__)))))

try:
    from generate_fake_data import (
        generate_users, generate_categories, generate_products, generate_variations,
        generate_reviews, generate_payments, generate_orders, generate_order_products,
        generate_carts, generate_cart_items, generate_organization, generate_hero_slides,
        generate_about_content, generate_testimonials, clear_existing_data
    )
except ImportError:
    # If import fails, we'll define a minimal version here
    def generate_users(count): return []
    def generate_categories(): return []
    def generate_products(categories, count): return []
    def generate_variations(products, count): return []
    def generate_reviews(users, products, count): return []
    def generate_payments(users, count): return []
    def generate_orders(users, payments, count): return []
    def generate_order_products(users, orders, products, variations, count): return []
    def generate_carts(count): return []
    def generate_cart_items(users, carts, products, variations, count): return []
    def generate_organization(): return None
    def generate_hero_slides(count): return []
    def generate_about_content(): return []
    def generate_testimonials(users, count): return []
    def clear_existing_data(): pass


class Command(BaseCommand):
    help = 'Generate fake data for MahBeauty application using Faker'

    def add_arguments(self, parser):
        parser.add_argument(
            '--clear',
            action='store_true',
            help='Clear existing data before generating new data',
        )
        parser.add_argument(
            '--users',
            type=int,
            default=50,
            help='Number of users to generate (default: 50)',
        )
        parser.add_argument(
            '--products',
            type=int,
            default=200,
            help='Number of products to generate (default: 200)',
        )
        parser.add_argument(
            '--variations',
            type=int,
            default=300,
            help='Number of variations to generate (default: 300)',
        )
        parser.add_argument(
            '--reviews',
            type=int,
            default=500,
            help='Number of reviews to generate (default: 500)',
        )
        parser.add_argument(
            '--orders',
            type=int,
            default=150,
            help='Number of orders to generate (default: 150)',
        )
        parser.add_argument(
            '--payments',
            type=int,
            default=100,
            help='Number of payments to generate (default: 100)',
        )
        parser.add_argument(
            '--order-products',
            type=int,
            default=400,
            help='Number of order products to generate (default: 400)',
        )
        parser.add_argument(
            '--carts',
            type=int,
            default=30,
            help='Number of carts to generate (default: 30)',
        )
        parser.add_argument(
            '--cart-items',
            type=int,
            default=100,
            help='Number of cart items to generate (default: 100)',
        )
        parser.add_argument(
            '--hero-slides',
            type=int,
            default=5,
            help='Number of hero slides to generate (default: 5)',
        )
        parser.add_argument(
            '--testimonials',
            type=int,
            default=50,
            help='Number of testimonials to generate (default: 50)',
        )

    def handle(self, *args, **options):
        self.stdout.write("=" * 60)
        self.stdout.write(self.style.SUCCESS("MahBeauty Fake Data Generator"))
        self.stdout.write("=" * 60)

        # Check if faker is installed
        try:
            from faker import Faker
        except ImportError:
            raise CommandError(
                "Faker is not installed. Please install it with: pip install faker pillow"
            )

        start_time = timezone.now()

        # Clear existing data if requested
        if options['clear']:
            self.stdout.write("Clearing existing data...")
            clear_existing_data()
            self.stdout.write(self.style.SUCCESS("Existing data cleared!"))

        self.stdout.write("\nStarting data generation...")

        try:
            # Generate data in dependency order
            users = generate_users(options['users'])
            categories = generate_categories()
            products = generate_products(categories, options['products'])
            variations = generate_variations(products, options['variations'])
            reviews = generate_reviews(users, products, options['reviews'])
            payments = generate_payments(users, options['payments'])
            orders = generate_orders(users, payments, options['orders'])
            order_products = generate_order_products(
                users, orders, products, variations, options['order_products']
            )
            carts = generate_carts(options['carts'])
            cart_items = generate_cart_items(
                users, carts, products, variations, options['cart_items']
            )

            # Store data
            organization = generate_organization()
            hero_slides = generate_hero_slides(options['hero_slides'])
            about_contents = generate_about_content()
            testimonials = generate_testimonials(users, options['testimonials'])

            end_time = timezone.now()
            duration = end_time - start_time

            self.stdout.write("\n" + "=" * 60)
            self.stdout.write(self.style.SUCCESS("DATA GENERATION COMPLETE!"))
            self.stdout.write("=" * 60)
            self.stdout.write(f"Total time taken: {duration}")
            self.stdout.write("\nSummary:")
            self.stdout.write(f"  Users: {len(users)}")
            self.stdout.write(f"  Categories: {len(categories)}")
            self.stdout.write(f"  Products: {len(products)}")
            self.stdout.write(f"  Variations: {len(variations)}")
            self.stdout.write(f"  Reviews: {len(reviews)}")
            self.stdout.write(f"  Payments: {len(payments)}")
            self.stdout.write(f"  Orders: {len(orders)}")
            self.stdout.write(f"  Order Products: {len(order_products)}")
            self.stdout.write(f"  Carts: {len(carts)}")
            self.stdout.write(f"  Cart Items: {len(cart_items)}")
            self.stdout.write(f"  Organization: 1")
            self.stdout.write(f"  Hero Slides: {len(hero_slides)}")
            self.stdout.write(f"  About Contents: {len(about_contents)}")
            self.stdout.write(f"  Testimonials: {len(testimonials)}")
            self.stdout.write(self.style.SUCCESS("\nYou can now test your application with realistic data!"))

        except Exception as e:
            raise CommandError(f"Error occurred during data generation: {e}")
