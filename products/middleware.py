from django.utils import timezone
from django.utils.deprecation import MiddlewareMixin
from .models import Product
import logging

logger = logging.getLogger(__name__)


class OfferExpirationMiddleware(MiddlewareMixin):
    """
    Middleware to automatically deactivate expired offers.
    This runs on every request to ensure real-time offer expiration.
    """
    
    def __init__(self, get_response):
        self.get_response = get_response
        self.last_check = None
        self.check_interval = 300  # Check every 5 minutes (300 seconds)
        super().__init__(get_response)
    
    def __call__(self, request):
        # Check if we should run the expiration check
        now = timezone.now()
        
        if (self.last_check is None or 
            (now - self.last_check).total_seconds() > self.check_interval):
            
            try:
                # Deactivate expired offers
                expired_count = Product.deactivate_expired_offers()
                
                if expired_count > 0:
                    logger.info(f"Automatically deactivated {expired_count} expired offers")
                
                self.last_check = now
                
            except Exception as e:
                logger.error(f"Error in offer expiration check: {e}")
        
        response = self.get_response(request)
        return response
