from django.contrib import admin
from django.utils.html import format_html
from django.urls import reverse
from django.db.models import Count, Avg
from django.contrib.admin import SimpleListFilter
from .models import ProductCategory, Product, Variation, ReviewRating


class ProductCountFilter(SimpleListFilter):
    title = 'Product Count'
    parameter_name = 'product_count'

    def lookups(self, request, model_admin):
        return (
            ('0', 'No Products'),
            ('1-5', '1-5 Products'),
            ('6-10', '6-10 Products'),
            ('10+', 'More than 10 Products'),
        )

    def queryset(self, request, queryset):
        if self.value() == '0':
            return queryset.annotate(product_count=Count('products')).filter(product_count=0)
        elif self.value() == '1-5':
            return queryset.annotate(product_count=Count('products')).filter(product_count__range=(1, 5))
        elif self.value() == '6-10':
            return queryset.annotate(product_count=Count('products')).filter(product_count__range=(6, 10))
        elif self.value() == '10+':
            return queryset.annotate(product_count=Count('products')).filter(product_count__gt=10)


@admin.register(ProductCategory)
class ProductCategoryAdmin(admin.ModelAdmin):
    list_display = ('name', 'product_count', 'avg_rating')
    search_fields = ('name',)
    ordering = ('name',)
    list_filter = (ProductCountFilter,)
    verbose_name = "Product Category"
    verbose_name_plural = "Product Categories"

    def get_queryset(self, request):
        queryset = super().get_queryset(request)
        queryset = queryset.annotate(
            _product_count=Count('products'),
            _avg_rating=Avg('products__rating')
        )
        return queryset

    def product_count(self, obj):
        # Try to get the annotated value first, fallback to manual calculation
        count = getattr(obj, '_product_count', None)
        if count is None:
            # Fallback: calculate manually if annotation failed
            count = obj.products.count()

        url = reverse('admin:products_product_changelist') + f'?category__id__exact={obj.id}'
        return format_html('<a href="{}">{} products</a>', url, count)
    product_count.short_description = 'Products'
    product_count.admin_order_field = '_product_count'

    def avg_rating(self, obj):
        # Try to get the annotated value first, fallback to manual calculation
        avg = getattr(obj, '_avg_rating', None)
        if avg is None:
            # Fallback: calculate manually if annotation failed
            from django.db.models import Avg
            avg_result = obj.products.aggregate(avg_rating=Avg('rating'))
            avg = avg_result.get('avg_rating')

        if avg is None:
            return '-'

        try:
            avg_float = float(avg)
            stars = '★' * int(avg_float) + '☆' * (5 - int(avg_float))
            title_text = f"{avg_float:.2f}"
            return format_html('<span title="{}">{}</span>', title_text, stars)
        except (ValueError, TypeError):
            return '-'
    avg_rating.short_description = 'Avg Rating'
    avg_rating.admin_order_field = '_avg_rating'


class StockFilter(SimpleListFilter):
    title = 'Stock Status'
    parameter_name = 'stock_status'

    def lookups(self, request, model_admin):
        return (
            ('in_stock', 'In Stock'),
            ('out_of_stock', 'Out of Stock'),
            ('low_rating', 'Low Rating (< 3.0)'),
            ('high_rating', 'High Rating (>= 4.0)'),
        )

    def queryset(self, request, queryset):
        if self.value() == 'in_stock':
            return queryset.filter(in_stock=True)
        elif self.value() == 'out_of_stock':
            return queryset.filter(in_stock=False)
        elif self.value() == 'low_rating':
            return queryset.filter(rating__lt=3.0)
        elif self.value() == 'high_rating':
            return queryset.filter(rating__gte=4.0)


class VariationInline(admin.TabularInline):
    model = Variation
    extra = 1
    fields = ('variation_category', 'variation_value', 'is_active')
    classes = ('collapse',)





@admin.register(Product)
class ProductAdmin(admin.ModelAdmin):
    list_display = ('image_preview', 'name', 'category', 'formatted_price', 'rating_display',
                   'stock_status', 'review_count', 'offer_status', 'is_on_offer', 'is_active', 'created_at')
    list_filter = ('category', StockFilter, 'is_active', 'is_on_offer', 'offer_is_active', 'created_at')
    search_fields = ('name', 'description', 'category__name')
    list_editable = ('is_active', 'is_on_offer')
    readonly_fields = ('created_at', 'updated_at', 'rating', 'image_preview_large', 'offer_summary')
    ordering = ('-created_at',)
    list_per_page = 20
    date_hierarchy = 'created_at'

    fieldsets = (
        ('Basic Information', {
            'fields': ('name', 'description', 'category', 'price', 'image', 'image_preview_large')
        }),
        ('Product Details', {
            'fields': ('ingredients', 'usage_instructions', 'benefits'),
            'classes': ('collapse',)
        }),
        ('Status & Availability', {
            'fields': ('in_stock', 'is_active', 'rating')
        }),
        ('Offer Settings', {
            'fields': ('is_on_offer', 'offer_price', 'offer_start_date', 'offer_end_date', 'offer_is_active', 'offer_summary'),
            'classes': ('collapse',)
        }),
        ('Timestamps', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )

    inlines = [VariationInline]

    actions = ['mark_as_active', 'mark_as_inactive', 'mark_in_stock', 'mark_out_of_stock',
               'enable_offer', 'disable_offer', 'extend_offer_7_days']

    def get_queryset(self, request):
        queryset = super().get_queryset(request)
        queryset = queryset.select_related('category').prefetch_related('reviewrating_set')
        return queryset

    def image_preview(self, obj):
        if obj.image:
            return format_html('<img src="{}" width="50" height="50" style="border-radius: 5px;" />', obj.image.url)
        return "No Image"
    image_preview.short_description = 'Image'

    def image_preview_large(self, obj):
        if obj.image:
            return format_html('<img src="{}" width="200" height="200" style="border-radius: 10px;" />', obj.image.url)
        return "No Image"
    image_preview_large.short_description = 'Product Image'

    def formatted_price(self, obj):
        price_text = f"NPR {float(obj.price):.2f}"
        return format_html('<strong>{}</strong>', price_text)
    formatted_price.short_description = 'Price'
    formatted_price.admin_order_field = 'price'

    def rating_display(self, obj):
        if obj.rating > 0:
            rating_float = float(obj.rating)
            stars = '★' * int(rating_float) + '☆' * (5 - int(rating_float))
            title_text = f"{rating_float:.1f}/5"
            return format_html('<span title="{}">{}</span>', title_text, stars)
        return format_html('<span style="color: #999;">No ratings</span>')
    rating_display.short_description = 'Rating'
    rating_display.admin_order_field = 'rating'

    def stock_status(self, obj):
        if obj.in_stock:
            return format_html('<span style="color: green; font-weight: bold;">✓ In Stock</span>')
        return format_html('<span style="color: red; font-weight: bold;">✗ Out of Stock</span>')
    stock_status.short_description = 'Stock'
    stock_status.admin_order_field = 'in_stock'

    def review_count(self, obj):
        count = obj.reviewrating_set.count()
        if count > 0:
            url = reverse('admin:products_reviewrating_changelist') + f'?product__id__exact={obj.id}'
            return format_html('<a href="{}">{} reviews</a>', url, count)
        return "No reviews"
    review_count.short_description = 'Reviews'

    def offer_status(self, obj):
        if obj.is_on_offer and obj.is_offer_valid():
            discount = float(obj.get_discount_percentage())
            discount_text = f"🏷️ {discount:.1f}% OFF"
            return format_html('<span style="color: green; font-weight: bold;">{}</span>', discount_text)
        elif obj.is_on_offer:
            return format_html('<span style="color: orange;">🏷️ Offer Set</span>')
        return "-"
    offer_status.short_description = 'Offer Status'

    def offer_summary(self, obj):
        if obj.is_on_offer:
            summary = f"Original Price: NPR {float(obj.price):.2f}<br>"
            if obj.offer_price:
                summary += f"Offer Price: NPR {float(obj.offer_price):.2f}<br>"
                summary += f"Discount: {obj.get_discount_percentage():.1f}%<br>"
                summary += f"Savings: NPR {float(obj.get_savings_amount()):.2f}<br>"
            summary += f"Valid: {'Yes' if obj.is_offer_valid() else 'No'}"
            return format_html(summary)
        return "No offer set"
    offer_summary.short_description = 'Offer Summary'

    # Custom actions
    def mark_as_active(self, request, queryset):
        updated = queryset.update(is_active=True)
        self.message_user(request, f'{updated} products marked as active.')
    mark_as_active.short_description = "Mark selected products as active"

    def mark_as_inactive(self, request, queryset):
        updated = queryset.update(is_active=False)
        self.message_user(request, f'{updated} products marked as inactive.')
    mark_as_inactive.short_description = "Mark selected products as inactive"

    def mark_in_stock(self, request, queryset):
        updated = queryset.update(in_stock=True)
        self.message_user(request, f'{updated} products marked as in stock.')
    mark_in_stock.short_description = "Mark selected products as in stock"

    def mark_out_of_stock(self, request, queryset):
        updated = queryset.update(in_stock=False)
        self.message_user(request, f'{updated} products marked as out of stock.')
    mark_out_of_stock.short_description = "Mark selected products as out of stock"

    def enable_offer(self, request, queryset):
        """Enable offers for selected products with default 10% discount"""
        from django.utils import timezone
        from datetime import timedelta

        updated = 0
        for product in queryset:
            if not product.is_on_offer:
                product.is_on_offer = True
                if not product.offer_price:
                    product.offer_price = product.price * 0.9  # 10% discount
                if not product.offer_start_date:
                    product.offer_start_date = timezone.now()
                if not product.offer_end_date:
                    product.offer_end_date = timezone.now() + timedelta(days=7)
                product.offer_is_active = True
                product.save()
                updated += 1

        self.message_user(request, f'{updated} products enabled for offers with 10% discount.')
    enable_offer.short_description = "Enable offers for selected products (10 percent discount, 7 days)"

    def disable_offer(self, request, queryset):
        """Disable offers for selected products"""
        updated = queryset.filter(is_on_offer=True).update(
            is_on_offer=False,
            offer_is_active=False
        )
        self.message_user(request, f'{updated} product offers disabled.')
    disable_offer.short_description = "Disable offers for selected products"

    def extend_offer_7_days(self, request, queryset):
        """Extend offers by 7 days for selected products"""
        from django.utils import timezone
        from datetime import timedelta

        updated = 0
        for product in queryset.filter(is_on_offer=True):
            if product.offer_end_date:
                product.offer_end_date += timedelta(days=7)
            else:
                product.offer_end_date = timezone.now() + timedelta(days=7)
            product.offer_is_active = True
            product.save()
            updated += 1

        self.message_user(request, f'{updated} product offers extended by 7 days.')
    extend_offer_7_days.short_description = "Extend offers by 7 days for selected products"


@admin.register(Variation)
class VariationAdmin(admin.ModelAdmin):
    list_display = ('product_link', 'variation_category', 'variation_value', 'status_display', 'is_active', 'created_date')
    list_filter = ('variation_category', 'is_active', 'created_date')
    search_fields = ('product__name', 'variation_value')
    list_editable = ('is_active',)
    ordering = ('-created_date',)
    list_per_page = 25
    verbose_name = "Product Variation"
    verbose_name_plural = "Product Variations"

    def get_queryset(self, request):
        return super().get_queryset(request).select_related('product')

    def product_link(self, obj):
        url = reverse('admin:products_product_change', args=[obj.product.pk])
        return format_html('<a href="{}">{}</a>', url, obj.product.name)
    product_link.short_description = 'Product'
    product_link.admin_order_field = 'product__name'

    def status_display(self, obj):
        if obj.is_active:
            return format_html('<span style="color: green;">✓ Active</span>')
        return format_html('<span style="color: red;">✗ Inactive</span>')
    status_display.short_description = 'Status'
    status_display.admin_order_field = 'is_active'








class RatingFilter(SimpleListFilter):
    title = 'Rating Range'
    parameter_name = 'rating_range'

    def lookups(self, request, model_admin):
        return (
            ('5', '5 Stars'),
            ('4', '4 Stars'),
            ('3', '3 Stars'),
            ('2', '2 Stars'),
            ('1', '1 Star'),
            ('high', 'High (4-5 Stars)'),
            ('low', 'Low (1-2 Stars)'),
        )

    def queryset(self, request, queryset):
        if self.value() in ['1', '2', '3', '4', '5']:
            rating = int(self.value())
            return queryset.filter(rating__gte=rating, rating__lt=rating + 1)
        elif self.value() == 'high':
            return queryset.filter(rating__gte=4)
        elif self.value() == 'low':
            return queryset.filter(rating__lte=2)


@admin.register(ReviewRating)
class ReviewRatingAdmin(admin.ModelAdmin):
    list_display = ('product_link', 'user_link', 'subject_preview', 'rating_display', 'created_at')
    list_filter = (RatingFilter, 'created_at')
    search_fields = ('product__name', 'user__email', 'user__first_name', 'user__last_name', 'subject', 'review')
    readonly_fields = ('created_at', 'updated_at', 'ip', 'review_preview')
    ordering = ('-created_at',)
    list_per_page = 25
    date_hierarchy = 'created_at'
    verbose_name = "Product Review & Rating"
    verbose_name_plural = "Product Reviews & Ratings"

    fieldsets = (
        ('Review Information', {
            'fields': ('product', 'user', 'subject', 'review', 'review_preview', 'rating')
        }),
        ('Metadata', {
            'fields': ('ip', 'created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )

    def get_queryset(self, request):
        return super().get_queryset(request).select_related('product', 'user')

    def product_link(self, obj):
        url = reverse('admin:products_product_change', args=[obj.product.pk])
        return format_html('<a href="{}">{}</a>', url, obj.product.name)
    product_link.short_description = 'Product'
    product_link.admin_order_field = 'product__name'

    def user_link(self, obj):
        if hasattr(obj.user, 'first_name') and obj.user.first_name:
            display_name = f"{obj.user.first_name} {obj.user.last_name or ''}".strip()
        else:
            display_name = obj.user.email
        url = reverse('admin:account_useraccount_change', args=[obj.user.pk])
        return format_html('<a href="{}">{}</a>', url, display_name)
    user_link.short_description = 'User'
    user_link.admin_order_field = 'user__email'

    def subject_preview(self, obj):
        if len(obj.subject) > 30:
            return f"{obj.subject[:30]}..."
        return obj.subject
    subject_preview.short_description = 'Subject'
    subject_preview.admin_order_field = 'subject'

    def rating_display(self, obj):
        rating_float = float(obj.rating)
        stars = '★' * int(rating_float) + '☆' * (5 - int(rating_float))
        color = 'green' if rating_float >= 4 else 'orange' if rating_float >= 3 else 'red'
        title_text = f"{rating_float:.1f}/5"
        return format_html('<span style="color: {}; font-size: 16px;" title="{}">{}</span>',
                          color, title_text, stars)
    rating_display.short_description = 'Rating'
    rating_display.admin_order_field = 'rating'

    def review_preview(self, obj):
        if obj.review:
            preview = obj.review[:200] + "..." if len(obj.review) > 200 else obj.review
            return format_html('<div style="max-width: 400px; padding: 10px; background: #f8f9fa; border-radius: 5px;">{}</div>', preview)
        return "No review text"
    review_preview.short_description = 'Review Preview'
