from django.db.models.signals import pre_save, post_save
from django.dispatch import receiver
from django.utils import timezone
from .models import Product
import logging

logger = logging.getLogger(__name__)


@receiver(pre_save, sender=Product)
def check_offer_expiration_on_save(sender, instance, **kwargs):
    """
    Signal to check and update offer status before saving a product.
    This ensures offers are automatically deactivated when expired.
    """
    if instance.is_on_offer and instance.offer_end_date:
        now = timezone.now()
        if instance.offer_end_date <= now and instance.offer_is_active:
            instance.offer_is_active = False
            logger.info(f"Auto-deactivated expired offer for product: {instance.name}")


@receiver(post_save, sender=Product)
def log_offer_changes(sender, instance, created, **kwargs):
    """
    Signal to log offer-related changes for monitoring.
    """
    if created and instance.is_on_offer:
        logger.info(f"New product with offer created: {instance.name}")
    elif not created and instance.is_on_offer:
        if instance.is_offer_valid():
            logger.info(f"Offer updated for product: {instance.name}")
        else:
            logger.info(f"Offer expired/deactivated for product: {instance.name}")
